<?php

/**
 * This file is part of the Carbon package.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

/*
 * Authors: <AUTHORS>
 */
return array_replace_recursive(require __DIR__.'/en.php', [
    'formats' => [
        'L' => 'DD.MM.YYYY',
    ],
    'months' => ['Mu<PERSON><PERSON>', 'Gashyantare', 'Werurwe', 'Mata', 'Gicuransi', 'Kamena', 'Nyakanga', '<PERSON>nama', '<PERSON><PERSON><PERSON>', 'Ukwakira', 'Ugushyingo', 'U<PERSON>bo<PERSON>'],
    'months_short' => ['Mut', 'Gas', 'Wer', 'Mat', 'Gic', 'Kam', 'Nya', 'Kan', 'Nze', 'Ukw', 'Ugu', 'Uku'],
    'weekdays' => ['Ku cyumweru', '<PERSON>wa mbere', '<PERSON>wa kabiri', '<PERSON>wa gatatu', '<PERSON>wa kane', '<PERSON>wa gatanu', '<PERSON>wa gatandatu'],
    'weekdays_short' => ['Mwe', 'Mbe', 'Kab', '<PERSON>tu', 'Kan', 'Gnu', 'Gnd'],
    'weekdays_min' => ['Mwe', 'Mbe', 'Kab', 'Gtu', 'Kan', 'Gnu', 'Gnd'],
    'first_day_of_week' => 1,
    'day_of_first_week_of_year' => 1,

    'second' => ':count vuna', // less reliable
    's' => ':count vuna', // less reliable
    'a_second' => ':count vuna', // less reliable

    'year' => 'aka :count',
    'y' => 'aka :count',
    'a_year' => 'aka :count',

    'month' => 'ezi :count',
    'm' => 'ezi :count',
    'a_month' => 'ezi :count',

    'week' => ':count icyumweru',
    'w' => ':count icyumweru',
    'a_week' => ':count icyumweru',

    'day' => ':count nsi',
    'd' => ':count nsi',
    'a_day' => ':count nsi',

    'hour' => 'saha :count',
    'h' => 'saha :count',
    'a_hour' => 'saha :count',

    'minute' => ':count -nzinya',
    'min' => ':count -nzinya',
    'a_minute' => ':count -nzinya',
]);
