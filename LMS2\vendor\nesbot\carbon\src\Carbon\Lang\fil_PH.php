<?php

/**
 * This file is part of the Carbon package.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

/*
 * Authors: <AUTHORS>
 * - <PERSON><PERSON><PERSON> (alohajaycee)
 */
return array_replace_recursive(require __DIR__.'/en.php', [
    'formats' => [
        'L' => 'MM/DD/YY',
    ],
    'months' => ['Enero', '<PERSON><PERSON>rero', '<PERSON><PERSON>', '<PERSON>bril', '<PERSON>', '<PERSON>nyo', '<PERSON>ly<PERSON>', '<PERSON><PERSON><PERSON>', 'Setyembre', 'Oktubre', 'Nobyembre', 'Di<PERSON>embre'],
    'months_short' => ['Ene', 'Peb', 'Mar', 'Abr', 'May', 'Hun', 'Hul', 'Ago', 'Set', 'Okt', 'Nob', 'Dis'],
    'weekdays' => ['<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'],
    'weekdays_short' => ['<PERSON>', 'Lu<PERSON>', '<PERSON>', 'Miy', 'Huw', 'Biy', 'Sab'],
    'weekdays_min' => ['Lin', 'Lun', 'Mar', 'Miy', 'Huw', 'Biy', 'Sab'],
    'day_of_first_week_of_year' => 1,
    'meridiem' => ['N.U.', 'N.H.'],

    'before' => ':time bago',
    'after' => ':time pagkatapos',

    'year' => ':count taon',
    'y' => ':count taon',
    'a_year' => ':count taon',

    'month' => ':count buwan',
    'm' => ':count buwan',
    'a_month' => ':count buwan',

    'week' => ':count linggo',
    'w' => ':count linggo',
    'a_week' => ':count linggo',

    'day' => ':count araw',
    'd' => ':count araw',
    'a_day' => ':count araw',

    'hour' => ':count oras',
    'h' => ':count oras',
    'a_hour' => ':count oras',

    'minute' => ':count minuto',
    'min' => ':count minuto',
    'a_minute' => ':count minuto',

    'second' => ':count segundo',
    's' => ':count segundo',
    'a_second' => ':count segundo',

    'ago' => ':time ang nakalipas',
    'from_now' => 'sa :time',
]);
